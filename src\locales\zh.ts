export default {
  common: {
    clear: '清空',
    copy: '复制',
    close: '关闭',
    download: '下载',
    loadExample: '加载示例',
    selectAll: '全选',
    clearSelection: '清空选择',
    extract: '提取',
    results: '结果',
    options: '选项',
    input: '输入',
    preview: '预览',
    statistics: '统计',
    fields: '字段',
    items: '项',
    found: '已找到',
    extracted: '已提取',
    with: '包含',
    total: '总数',
    unique: '唯一',
    nonEmpty: '非空',
    loading: '加载中...',
  },
  navigation: {
    tools: '工具',
    title: '专业Web开发者工具',
    language: '语言',
    categories: '工具分类',
    menu: '菜单',
    close: '关闭',
    search: '搜索工具...',
    noResults: '没有找到匹配的工具。',
    noToolsInCategory: '该分类中没有可用的工具。',
  },
  homepage: {
    title: '开发者工具集合',
    subtitle: '为开发者、设计师和内容创作者提供强大的在线工具。提升您的工作效率。',
    recommendedTools: '推荐工具',
    exploreCategories: '浏览分类',
    stats: {
      totalTools: '工具总数',
      activeTools: '可用工具',
      categories: '分类数量',
      comingSoon: '即将推出',
    },
  },
  notFound: {
    title: '页面未找到',
    description: '您要查找的工具或页面不存在或已被移动。',
    backToHome: '返回首页',
    goBack: '返回上页',
    popularTools: '热门工具',
    helpText: '如果您需要帮助寻找特定工具，请查看侧边栏中的分类。',
  },
  toast: {
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    copied: '结果已复制到剪贴板！',
    copyFailed: '复制到剪贴板失败',
    downloadSuccess: '文件下载成功！',
  },
  footer: {
    madeWith: '用',
    by: '制作',
  },
  categories: {
    webTools: {
      name: '网页工具',
      description: '网页开发和分析工具',
    },
    jsonTools: {
      name: 'JSON工具',
      description: '全面的JSON处理和转换实用程序',
    },
    dataTools: {
      name: '数据工具',
      description: '数据处理和操作工具',
    },
    imageTools: {
      name: '图片工具',
      description: '图片处理和管理工具',
    },
    converters: {
      name: '转换器',
      description: '格式转换实用工具',
    },
    generators: {
      name: '生成器',
      description: '代码和内容生成器',
    },
  },
  pagination: {
    previous: '上一页',
    next: '下一页',
    page: '第',
    of: '页，共',
  },
  status: {
    active: '可用',
    'coming-soon': '即将推出',
  },
  tools: {
    htmlExtractor: {
      title: 'HTML 内容提取器',
      description: '一键从 HTML 代码中提取图片、视频、链接和其他资源',
      contentTypes: '内容类型',
      baseUrl: '基础 URL',
      inputPlaceholder: '请在此处粘贴您的 HTML 代码...',
      extractionResults: '转换结果',
      noResults: '暂无提取结果。请输入 HTML 代码并选择要提取的内容类型。',
      features: {
        imageExtraction: {
          title: '图片提取',
          description:
            '自动从 HTML 中提取所有图片 URL，包括 img 标签和 CSS 背景图片。支持相对路径转绝对路径，方便使用。',
        },
        mediaProcessing: {
          title: '媒体处理',
          description:
            '批量提取视频和音频文件链接，支持多种格式（MP4、WebM、Ogg、MP3 等）。自动识别 video 和 audio 标签中的源文件。',
        },
        linkAnalysis: {
          title: '链接分析',
          description:
            '提取页面中的所有超链接，包括 a 标签的 href 属性。支持筛选内部和外部链接，帮助分析网站结构。',
        },
      },
      options: {
        uniqueOnly: '仅显示唯一结果',
        absoluteUrls: '转换为绝对 URL',
      },
      types: {
        images: '图片',
        videos: '视频',
        audio: '音频',
        links: '链接',
        css: 'CSS',
        javascript: 'JavaScript',
        iframes: '内嵌框架',
        metadata: '元数据',
        forms: '表单',
      },
    },
    jsonFormatter: {
      title: 'JSON 格式化工具',
      description: '格式化、美化和验证 JSON 数据，支持自定义缩进',
      inputTitle: '输入 JSON',
      outputTitle: '格式化后的 JSON',
      inputPlaceholder: '在此粘贴您的 JSON 数据...',
      noResults: '暂无格式化结果。请输入有效的 JSON 进行格式化。',
      validJson: '有效的 JSON',
      invalidJson: '无效的 JSON',
      formattingComplete: '格式化完成',
      formatOptions: '格式选项',
      indent: '缩进',
      sortKeys: '排序键名',
      compactFormat: '紧凑格式',
      escapeUnicode: '转义 Unicode',
      formatJson: '格式化 JSON',
      spaces2: '2个空格',
      spaces4: '4个空格',
      tab: '制表符',
      lines: '行',
      characters: '字符',
      smaller: '减少',
      larger: '增加',
      features: {
        prettyFormat: {
          title: '美化格式',
          description: '自动格式化和美化 JSON，具有适当的缩进和间距',
        },
        validation: {
          title: '实时验证',
          description: '实时 JSON 验证，提供详细的错误信息和行号',
        },
        customization: {
          title: '自定义选项',
          description: '选择缩进大小、排序和紧凑格式化选项',
        },
      },
      messages: {
        formatSuccess: 'JSON 格式化成功！',
        formatError: '格式化 JSON 失败：',
        provideData: '请提供要格式化的 JSON 数据',
        copied: 'JSON 已复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '下载文件失败',
      },
    },
    jsonToExcel: {
      title: 'JSON 转 Excel/CSV/SQL 转换器',
      description: '将 JSON 数据转换为 Excel、CSV 或 SQL 格式，支持自定义选项',
      inputTitle: '输入 JSON 数据',
      outputTitle: 'Excel 输出结果',
      csvOutputTitle: 'CSV 输出结果',
      sqlOutputTitle: 'SQL 输出结果',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      noResults: '暂无转换结果。请输入 JSON 数据以进行转换。',
      conversionComplete: '转换成功完成！',
      readyForDownload: 'Excel 文件已准备就绪，可立即下载。',
      csvReadyForDownload: 'CSV 文件已准备就绪，可立即下载。',
      sqlReadyForDownload: 'SQL 文件已准备就绪，可立即下载。',
      previewTitle: '数据预览',
      convertToExcel: '转换为 Excel',
      convertToCsv: '转换为 CSV',
      convertToSql: '生成 SQL',
      showingRows: '显示 {shown} 行，共 {total} 行',
      options: {
        conversionType: '转换类型',
        includeHeaders: '包含表头',
        autoFitColumns: '自动适配列宽',
        sheetName: '工作表名称',
        sheetNamePlaceholder: '输入工作表名称',
        delimiter: '分隔符',
        quoteChar: '引号字符',
        flattenNested: '展开嵌套对象',
        comma: '逗号',
        semicolon: '分号',
        tab: '制表符',
        pipe: '管道符',
        doubleQuote: '双引号',
        singleQuote: '单引号',
        none: '无',
        sqlOptions: 'SQL 选项',
        tableName: '表名',
        tableNamePlaceholder: '输入表名',
        sqlType: 'SQL 类型',
        whereField: 'WHERE 字段',
        whereFieldPlaceholder: 'WHERE 子句的字段',
        escapeValues: '转义值',
        batchInsert: '批量插入',
      },
      features: {
        conversion: {
          title: '智能转换',
          description: '自动将 JSON 数组转换为 Excel、CSV 或 SQL 格式，并对数据类型进行妥善处理。',
        },
        formatting: {
          title: 'Excel 格式优化',
          description: '生成格式规范的 Excel 文件，包含表头、自动调整列宽及多工作表支持。',
        },
        batch: {
          title: '批量处理',
          description: '支持高效处理大型数据集，提供数据预览与批量下载功能。',
        },
      },
      errors: {
        emptyInput: '请提供待转换的 JSON 数据',
        invalidJson: 'JSON 格式无效，请检查您的输入内容。',
        notArray: '输入内容必须是 JSON 数组',
        emptyArray: 'JSON 数组不能为空',
        conversionFailed: 'JSON 转换失败',
        emptyTableName: '请提供表名',
        emptyWhereField: '请为 UPDATE 语句提供 WHERE 字段',
      },
      success: {
        conversionComplete: 'JSON 成功转换为 Excel！',
        csvConversionComplete: 'JSON 成功转换为 CSV！',
        sqlConversionComplete: 'SQL 语句生成成功！',
      },
    },
    excelToJson: {
      title: 'Excel 转 JSON 转换器 ',
      description: ' 将 Excel 文件转换为 JSON 格式，支持灵活的解析选项 ',
      inputTitle: ' 上传 Excel 文件 ',
      outputTitle: 'JSON 输出结果 ',
      uploadDescription: ' 选择需转换为 JSON 的 Excel 文件 ',
      selectFile: ' 选择 Excel 文件 ',
      supportedFormats: ' 支持 .xlsx、.xls、.csv 及 .ods 格式文件 ',
      noResults: ' 暂无转换结果。请上传 Excel 文件。',
      conversionComplete: ' 转换成功完成！',
      recordsCount: ' 已转换 {count} 条记录 ',
      convert: ' 转换为 JSON',
      fileSelected: ' 文件选择成功 ',
      options: {
        title: ' 转换选项 ',
        firstRowAsHeaders: ' 首行作为表头 ',
        skipEmptyRows: ' 跳过空行 ',
        sheetIndex: ' 待转换工作表 ',
      },
      features: {
        parsing: {
          title: 'Excel 解析 ',
          description: ' 解析 Excel 文件，支持多工作表、公式及多种数据类型。',
        },
        conversion: {
          title: ' 灵活转换 ',
          description: ' 转换时可配置表头、空行处理及特定工作表选择等选项。',
        },
        options: {
          title: ' 转换选项配置 ',
          description: ' 通过表头处理、空行跳过及工作表选择等功能自定义输出结果。',
        },
      },
      errors: {
        noFileSelected: ' 请选择需转换的 Excel 文件 ',
        xlsxRequired: 'Excel 文件解析需依赖 XLSX 库。请安装：npm install xlsx',
        conversionFailed: 'Excel 转 JSON 转换失败 ',
      },
    },
    jsonExtractor: {
      title: 'JSON 字段提取器',
      description: '一键从 JSON 数组数据中提取指定字段',
      availableFields: '可用字段',
      inputTitle: '输入 JSON 数组',
      inputNote: '请粘贴格式为以下的 JSON 数组数据：',
      inputDescription: '工具将自动解析 JSON 并列出所有可选择的字段。',
      inputPlaceholder: '请在此处粘贴您的 JSON 数据',
      extractedData: '提取的数据',
      fieldStatistics: '字段统计',
      noResults: '暂无提取结果。请输入 JSON 数组数据并选择要提取的字段。',
      options: {
        preserveStructure: '保持对象结构',
        removeEmpty: '移除空值',
        flattenNested: '展平嵌套对象',
      },
      features: {
        fieldExtraction: {
          title: '字段提取',
          description:
            '自动解析 JSON 数组并提取选定字段。支持嵌套对象并保持数据类型以确保准确提取。',
        },
        smartFiltering: {
          title: '智能过滤',
          description:
            '选择要包含在输出中的特定字段。可选择移除空值并保持原始对象结构以获得清晰的结果。',
        },
        exportOptions: {
          title: '导出选项',
          description:
            '将提取的数据复制到剪贴板或下载为 JSON 文件。包括字段统计和数据分析，便于更好地理解您的数据集。',
        },
      },
      errors: {
        invalidFormat: '输入必须是 JSON 数组格式：[{},{},...]',
        emptyArray: 'JSON 数组不能为空',
        noFields: '请选择至少一个字段进行提取',
        invalidJson: '无效的 JSON 格式：',
        noData: '请提供要提取的 JSON 数据',
      },
    },
    imageListProcessor: {
      title: '图片列表处理器',
      description: '输入图片URL列表并以可视化图库格式显示',
      inputTitle: '输入图片URL',
      inputNote: '请在下方粘贴图片URL，每行一个：',
      inputPlaceholder:
        '在此粘贴图片URL，每行一个...\n\n示例：\nhttps://example.com/image1.jpg\nhttps://example.com/image2.png\nhttps://example.com/image3.webp',
      imagePreview: '图片图库',
      noResults: '未找到有效的图片URL。请输入有效的图片URL。',
      imageError: '加载失败',
      emptyState: {
        title: '没有图片可显示',
        description: '在上方输入一些图片URL以在下方图库中查看。',
      },
      features: {
        simple: {
          title: '简单输入',
          description: '只需逐行粘贴图片URL - 无需复杂格式。',
        },
        gallery: {
          title: '增强图库',
          description: '在清洁4列布局中查看所有图片，支持缩放、平移和键盘导航的全功能灯箱预览。',
        },
        fast: {
          title: '专业预览',
          description: '高级图片查看器，支持缩放、滚动、拖拽和全屏功能，便于详细检查。',
        },
      },
    },
    videoToGifConverter: {
      title: '视频转GIF工具',
      description: '将视频转换为动态GIF，支持自定义文字叠加和时间控制',
      upload: {
        title: '上传视频',
        dragDrop: '拖拽视频文件到此处',
        selectFile: '选择视频文件',
        supportedFormats: '支持 MP4、AVI、MOV、WebM 等视频格式（最大：100MB）',
      },
      settings: {
        width: 'GIF宽度（像素）',
        quality: '质量',
        fps: '帧率（FPS）',
        qualityOptions: {
          high: '高质量',
          medium: '中等质量',
          low: '低质量（文件更小）',
        },
      },
      preview: {
        title: '视频预览和控制',
      },
      actions: {
        startCapture: '开始取图',
        stopCapture: '取图完成',
        generateGif: '生成GIF',
      },
      timeRange: {
        title: '时间范围选择',
        start: '开始',
        end: '结束',
        setStart: '设置开始时间',
        setEnd: '设置结束时间',
      },
      textOverlay: {
        title: '文字叠加',
        add: '添加文字',
        text: '文字',
        placeholder: '输入叠加文字...',
        startTime: '开始时间（秒）',
        endTime: '结束时间（秒）',
        fontSize: '字体大小',
        color: '颜色',
        position: '位置',
        positions: {
          top: '顶部',
          center: '居中',
          bottom: '底部',
        },
      },
      processing: {
        title: '处理视频中',
        description: '正在将您的视频转换为GIF并添加文字叠加。这可能需要一些时间...',
      },
      result: {
        title: '生成的GIF',
        download: '下载GIF',
        createNew: '创建新的GIF',
      },
      features: {
        conversion: {
          title: '视频转换',
          description: '将视频转换为高质量的动态GIF，支持自定义帧率和尺寸。',
        },
        textOverlay: {
          title: '文字叠加',
          description: '添加多个文字叠加，支持精确时间控制、自定义颜色、字体和位置。',
        },
        customization: {
          title: '全面自定义',
          description: '控制质量、大小、时间和文字外观等各个方面，获得完美效果。',
        },
      },
      errors: {
        invalidFile: '请选择有效的视频文件。',
        fileTooLarge: '文件大小必须小于100MB。',
        processingFailed: '视频处理失败，请重试。',
      },
    },
    apngGenerator: {
      title: 'APNG动图生成器',
      description: '将多张静态图片制作成动态PNG文件，支持自定义动画设置',
      uploadTitle: '上传图片帧',
      uploadDescription: '拖拽多张图片或点击选择动画帧',
      selectFiles: '选择图片文件',
      supportedFormats: '支持格式',
      settings: '动画设置',
      frameDelay: '帧延迟',
      loopCount: '循环次数',
      infinite: '无限循环',
      outputWidth: '输出宽度',
      outputHeight: '输出高度',
      advancedOptions: '高级选项',
      maintainAspectRatio: '保持宽高比',
      optimizeSize: '优化文件大小',
      frameList: '动画帧列表',
      generateAPNG: '生成APNG',
      generating: '生成中...',
      preview: '预览动画',
      animationPreview: '动画预览',
      downloadAPNG: '下载APNG',
      reorderHint: '帧将按上述顺序播放动画。您可以通过点击×按钮删除不需要的帧。',
      features: {
        title: '主要功能',
        highQuality: {
          title: '高质量输出',
          description: '生成无损动态PNG文件，支持透明度和24位色彩',
        },
        customizable: {
          title: '完全可定制',
          description: '控制帧时间、循环次数、尺寸和优化设置',
        },
        easyToUse: {
          title: '易于使用',
          description: '简单的拖拽界面，实时预览和即时下载',
        },
      },
    },
    backgroundRemover: {
      title: '在线抠图工具',
      description: '使用AI技术自动去除图片背景',
      features: {
        aiPowered: {
          title: 'AI智能识别',
          description: '先进的机器学习算法，精确检测和去除背景',
        },
        fastProcessing: {
          title: '快速处理',
          description: '秒级背景去除，高质量效果',
        },
        highQuality: {
          title: '高质量输出',
          description: '保持图像质量和细节，干净地去除背景',
        },
      },
      upload: {
        title: '上传图片',
        dragDrop: '拖拽图片到此处',
        supportedFormats: '支持JPG、PNG、GIF等图片格式',
        selectFile: '选择图片',
      },
      preview: {
        original: '原始图片',
        originalAlt: '原始图片',
        processed: '去背景后',
        processedAlt: '去除背景后的图片',
      },
      options: {
        title: '输出选项',
        model: 'AI模型',
        outputFormat: '输出格式',
        transparent: '透明背景',
        whiteBackground: '白色背景',
        backgroundColor: '背景颜色',
        quality: '输出质量',
      },
      models: {
        small: '小型（快速）',
        medium: '中型（平衡）',
        large: '大型（最佳质量）',
      },
      actions: {
        remove: '去除背景',
      },
      processing: {
        inProgress: '处理中...',
        analyzing: '正在分析图片并去除背景...',
        pleaseWait: '这可能需要几秒钟',
      },
      result: {
        title: '处理结果',
        noResult: '暂无处理结果。请上传图片以去除背景。',
        complete: '背景去除完成',
        ready: '您的图片已准备好下载',
      },
      imageInfo: {
        size: '文件大小',
        format: '格式',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '使用主体边界清晰的高分辨率图片以获得最佳效果',
        tip2: '主体与背景对比度好的图片效果更佳',
        tip3: '避免使用复杂背景或与主体颜色相似的图片',
        tip4: 'PNG格式保持透明度，JPG格式使用白色背景',
        tip5: '使用对比视图并排查看处理前后的结果',
      },
      comparison: {
        before: '处理前',
        after: '处理后',
      },
    },
    csvtojson: {
      title: 'CSV转JSON工具',
      description: '将CSV数据转换为JSON格式，支持自定义解析选项',
      introduction: {
        title: '工具介绍',
        description: '在线CSV转JSON工具，用于将固定符号分隔的CSV格式数据转换为JSON格式数据。',
        usage:
          '默认是以制表符(\\t)为数据字段分隔符，如是其它符号，直接在分隔符输入框中填写即可。支持将CSV转换为JSON对象或JSON数组。',
      },
      example: {
        title: '示例',
        input: 'CSV输入：',
        output: 'JSON输出：',
      },
      input: {
        title: '输入CSV数据',
        placeholder: '在此粘贴您的CSV数据...\n\n示例：\nname,age,score\n李华,25,89\n小明,22,85',
        fileUpload: '上传CSV文件',
      },
      options: {
        title: '解析选项',
        delimiter: '分隔符',
        outputFormat: '输出格式',
        hasHeaders: '首行作为标题',
        skipEmptyLines: '跳过空行',
        autoDetectNumbers: '自动检测数字',
        autoDetectBooleans: '自动检测布尔值',
      },
      delimiters: {
        comma: '逗号 (,)',
        semicolon: '分号 (;)',
        tab: '制表符 (\\t)',
        pipe: '管道符 (|)',
        space: '空格',
      },
      formats: {
        jsonObject: 'JSON对象',
        jsonArray: 'JSON数组',
      },
      preview: {
        title: '数据预览',
        firstRows: '前{count}行',
        rowsDetected: '检测到{count}行',
      },
      convert: '转换为JSON',
      output: {
        title: 'JSON输出',
        complete: '转换完成',
        recordsConverted: '已转换{count}条记录',
        noOutput: '暂无JSON输出。请输入CSV数据进行转换。',
      },
    },
    excelTextToJson: {
      title: 'Excel文本转JSON',
      description: '将Excel剪贴板数据直接转换为JSON格式',
      introduction: {
        title: '工具介绍',
        description: '在线Excel文本转JSON工具，用于将制表符分隔的Excel数据转换为JSON格式。',
        usage:
          '从Excel中复制数据并粘贴到此处。默认分隔符为制表符(\\t)。对象格式需要第一行包含标题。',
      },
      example: {
        title: '示例',
        input: 'Excel输入：',
        output: 'JSON输出：',
      },
      input: {
        title: '输入Excel数据',
        placeholder:
          '在此粘贴您的Excel数据...\n\n示例：\nname\tage\tscore\n李华\t25\t89\n小明\t22\t85',
        fileUpload: '上传文本文件',
      },
      options: {
        title: '解析选项',
        delimiter: '分隔符',
        outputFormat: '输出格式',
        hasHeaders: '首行作为标题',
        skipEmptyLines: '跳过空行',
        autoDetectNumbers: '自动检测数字',
        autoDetectBooleans: '自动检测布尔值',
      },
      delimiters: {
        comma: '逗号 (,)',
        semicolon: '分号 (;)',
        tab: '制表符 (\\t)',
        pipe: '管道符 (|)',
        space: '空格',
      },
      formats: {
        jsonObject: 'JSON对象',
        jsonArray: 'JSON数组',
      },
      preview: {
        title: '数据预览',
        firstRows: '前{count}行',
        rowsDetected: '检测到{count}行',
      },
      convert: '转换为JSON',
      output: {
        title: 'JSON输出',
        complete: '转换完成',
        recordsConverted: '已转换{count}条记录',
        noOutput: '暂无JSON输出。请输入Excel数据进行转换。',
      },
    },
    jsonPathExtractor: {
      title: 'JSON 路径提取器',
      description: '使用 JSONPath 表达式从 JSON 中提取数据，支持高级过滤功能',
      extractButton: '提取数据',
      features: {
        pathExtraction: {
          title: '路径提取',
          description:
            '使用 JSONPath 表达式从复杂的 JSON 结构中精准提取数据，支持点记号和数组索引。',
        },
        filtering: {
          title: '高级过滤',
          description: '支持通配符、数组切片和条件过滤，精确提取您需要的数据。',
        },
        export: {
          title: '导出结果',
          description: '将提取的数据复制到剪贴板或下载为 JSON 文件，包含格式化输出和统计信息。',
        },
      },
      syntaxGuide: {
        title: 'JSONPath 语法指南',
        basicSyntax: '基本语法',
        examples: '常用示例',
        rootSymbol: 'JSON 数据的根',
        dotNotation: '访问对象属性',
        bracketNotation: '数组/对象访问',
        wildcard: '匹配所有元素',
        exampleDesc1: '获取第一本书的标题',
        exampleDesc2: '获取所有书的作者',
      },
      inputSection: {
        title: 'JSON 数据和路径',
        jsonData: 'JSON 数据',
        jsonPath: 'JSONPath 表达式',
        jsonPlaceholder: '在此粘贴您的 JSON 数据...',
        pathPlaceholder: '输入 JSONPath 表达式（例如：$.users[*].name）',
        quickPaths: '快速路径模板',
      },
      outputSection: {
        title: '提取结果',
        noResults: '暂无提取结果。请输入 JSON 数据和 JSONPath 表达式。',
        extractedData: '提取的数据',
      },
      quickPaths: {
        root: '根元素',
        allProperties: '所有属性',
        firstArrayItem: '第一个数组项',
        allArrayItems: '所有数组项',
        lastArrayItem: '最后一个数组项',
        arraySlice: '数组切片 (0-2)',
      },
      success: {
        validJson: '有效的 JSON 格式',
        extracted: '数据提取成功',
        arrayResults: '找到 {count} 个数组项',
        objectResults: '找到包含 {count} 个属性的对象',
        primitiveResult: '找到 {type} 类型的值',
      },
      errors: {
        invalidJson: '无效的 JSON 格式',
        pathError: 'JSONPath 表达式错误',
        noMatches: '没有数据匹配指定的路径',
      },
      messages: {
        copied: '提取的数据已成功复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '文件下载失败',
      },
    },
    jsonMerge: {
      title: 'JSON 文件合并工具',
      description: '将多个 JSON 文件合并成一个文件',
      introduction: {
        title: '工具介绍',
        description: '在线 JSON 文件合并工具，将多个 JSON 文件合并成一个大的 JSON 文件。',
        usage: 'JSON 文件将按导入顺序依次合并，如对顺序有要求，请注意文件顺序。',
      },
      fileUpload: {
        title: '上传 JSON 文件',
        description: '选择多个 JSON 文件进行合并。文件将按以下顺序处理。',
        selectFiles: '选择 JSON 文件',
        supportedFormats: '支持 .json 文件',
        noFiles: '尚未选择文件。请选择要合并的 JSON 文件。',
      },
      filePreview: {
        title: '文件预览',
        fileName: '文件名',
        fileSize: '文件大小',
        jsonStructure: 'JSON 结构',
        arrayItems: '{count} 个数组项',
        object: 'JSON 对象',
        remove: '移除',
        moveUp: '上移',
        moveDown: '下移',
      },
      options: {
        title: '合并选项',
        outputFileName: '输出文件名',
        outputFileNamePlaceholder: '输入输出文件名（不含扩展名）',
        defaultFileName: '合并的JSON',
      },
      actions: {
        merge: '合并 JSON 文件',
        clear: '清空所有文件',
        download: '下载合并的 JSON',
      },
      output: {
        title: '合并的 JSON 输出',
        noOutput: '暂无合并输出。请上传 JSON 文件并点击合并。',
        complete: '合并完成',
        itemsMerged: '已合并 {count} 项',
        downloadReady: '合并的 JSON 文件已准备好下载。',
      },
      features: {
        multipleFiles: {
          title: '多文件支持',
          description: '通过拖放支持上传和合并多个 JSON 文件。',
        },
        orderControl: {
          title: '顺序控制',
          description: '合并前重新排序文件以控制输出顺序。',
        },
        preview: {
          title: '文件预览',
          description: '合并前预览文件结构和内容。',
        },
      },
      errors: {
        noFiles: '请至少选择一个 JSON 文件进行合并',
        invalidJson: '文件中的 JSON 无效：{fileName}',
        mergeFailed: '合并 JSON 文件失败：{error}',
        emptyArray: 'JSON 文件的根级别必须包含一个数组',
      },
      success: {
        filesAdded: '已成功添加 {count} 个文件',
        mergeComplete: 'JSON 文件合并成功！',
      },
    },
    cookieToJson: {
      title: 'Cookie 转 JSON',
      description: '将 Cookie 字符串转换为 JSON 对象，支持多种解析选项',
      inputTitle: '输入 Cookie 字符串',
      outputTitle: 'JSON 输出',
      inputNote: '粘贴格式如下的 Cookie 字符串：',
      inputPlaceholder:
        '请在此粘贴您的 Cookie 字符串，例如：\nsessionId=abc123; userId=12345; theme=dark; lang=zh-CN\n\n支持的格式：\n- 标准 Cookie 格式：name1=value1; name2=value2\n- 自动解码 URL 编码的值\n- 处理无值的 Cookie（标志位）',
      parseOptions: '解析选项',
      noResults: '暂无转换结果。请输入 Cookie 字符串进行转换。',
      error: '解析错误',
      success: '解析成功',
      conversionComplete: '转换完成',
      cookiesFound: '找到 {count} 个 Cookie',
      statistics: '共 {total} 个 Cookie，{nonEmpty} 个有值',
      options: {
        decodeValues: '解码 URL 编码值',
        removeEmpty: '移除空值',
        formatOutput: '格式化 JSON 输出',
      },
      features: {
        parsing: {
          title: 'Cookie 解析',
          description: '自动解析 Cookie 字符串，支持标准 HTTP Cookie 格式和 URL 解码。',
        },
        conversion: {
          title: 'JSON 转换',
          description: '将解析的 Cookie 转换为清晰的 JSON 格式，支持可定制的输出格式选项。',
        },
        export: {
          title: '导出选项',
          description: '复制到剪贴板或下载为 JSON 文件，包含统计信息和验证反馈。',
        },
      },
      errors: {
        noCookies: '输入字符串中未找到有效的 Cookie',
        parseError: '解析 Cookie 字符串失败：{error}',
      },
      messages: {
        copied: 'JSON 已成功复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '文件下载失败',
      },
    },
    fileRenamer: {
      title: '文件重命名工具',
      description: '多模式批量重命名文件 - 本地处理保护隐私',
      uploadArea: {
        title: '拖放文件到此处',
        subtitle: '或点击选择文件',
        selectFiles: '选择文件',
      },
      fileCount: '文件总数：{count}',
      totalSize: '总大小：{size}',
      tabs: {
        sequential: '顺序编号',
        replace: '查找替换',
        case: '大小写转换',
        insert: '插入文本',
        truncate: '截取文本',
        script: '生成脚本',
      },
      sequential: {
        prefix: '前缀',
        prefixPlaceholder: '例如：photo_',
        startNumber: '起始数字',
        padding: '数字补位',
      },
      replace: {
        findText: '查找文本',
        findPlaceholder: '要查找的文本',
        replaceText: '替换为',
        replacePlaceholder: '替换文本',
        caseSensitive: '区分大小写',
      },
      case: {
        transformation: '大小写转换',
        uppercase: '全部大写',
        lowercase: '全部小写',
        capitalize: '首字母大写',
      },
      insert: {
        text: '插入文本',
        textPlaceholder: '要插入的文本',
        position: '插入位置',
        prefix: '文件名开头',
        suffix: '文件名结尾',
        atIndex: '指定位置',
        index: '插入索引',
      },
      truncate: {
        startIndex: '开始索引',
        endIndex: '结束索引',
        description: '从开始索引到结束索引提取子字符串（从0开始计数）',
      },
      script: {
        scriptType: '脚本类型',
        windows: 'Windows 批处理 (.bat)',
        linux: 'Linux Shell (.sh)',
        autoGenerated: '自动生成脚本',
        scriptPreview: '脚本预览',
        downloadScript: '下载脚本',
        copyScript: '复制脚本',
        noContent: '暂无脚本内容。添加文件并应用重命名选项以生成脚本。',
        instructions: {
          title: '使用说明',
          description:
            '此工具生成用于重命名文件的脚本。脚本会根据您的文件和重命名选项自动生成。点击"下载脚本"进行下载。将脚本放置在包含文件的目录中，然后运行它来执行重命名操作。',
        },
      },
      actions: {
        preview: '预览',
        apply: '应用重命名',
        download: '下载ZIP',
        clear: '清空文件',
      },
      sorting: {
        title: '排序',
        natural: '自然排序',
        filename: '文件名顺序',
        modifiedTime: '文件修改时间顺序',
        modifiedTimeDesc: '修改时间倒序',
        random: '随机排序',
        reverse: '反转当前排序',
        manual: '手动排序（拖拽）',
      },
      fileList: {
        title: '文件列表',
        drag: '拖拽',
        originalName: '原始名称',
        newName: '新名称',
        size: '大小',
        type: '类型',
        dragHint: '拖拽文件以手动重新排序',
      },
      messages: {
        filesAdded: '成功添加 {count} 个文件！',
        renameApplied: '重命名应用成功！',
        downloadStarted: '下载已开始！请检查您的下载文件夹。',
        downloadError: '下载失败！请重试。',
        filesCleared: '所有文件已清空！',
        noFilesToProcess: '没有要处理的文件！请先添加文件。',
        noScriptToDownload: '没有可下载的脚本！请先生成脚本。',
        noScriptToCopy: '没有可复制的脚本！请先生成脚本。',
        scriptDownloaded: '脚本 "{fileName}" 下载成功！',
        scriptCopied: '脚本已成功复制到剪贴板！',
        scriptCopyFailed: '复制脚本到剪贴板失败！',
      },
    },
    imageCompressor: {
      title: '压图大师',
      description: '高效的在线图片压缩工具，支持批量处理和本地隐私保护',
      settings: '压缩设置',
      quality: '质量',
      smaller: '更小',
      larger: '更大',
      outputFormat: '输出格式',
      keepOriginal: '保持原格式',
      maxWidth: '最大宽度',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '支持多张图片，本地处理，不上传到服务器',
      supportedFormats: '支持格式',
      selectFiles: '选择文件',
      imageList: '图片列表',
      compressing: '压缩中...',
      compressAll: '全部压缩',
      downloadAll: '下载全部',
      compress: '压缩',
      remove: '移除',
      originalSize: '原始大小',
      compressedSize: '压缩后大小',
      spaceSaved: '节省空间',
      original: '原始',
      compressed: '压缩后',
      imagePreview: '图片预览',
      originalImage: '原始图片',
      compressedImage: '压缩后图片',
      size: '大小',
      dimensions: '尺寸',
      saved: '节省',
      status: {
        pending: '等待中',
        compressing: '处理中',
        completed: '已完成',
        error: '失败',
      },
      features: {
        efficient: {
          title: '高效压缩',
          description: '先进的压缩算法在保持图片质量的同时减少文件大小40-80%。',
        },
        secure: {
          title: '隐私保护',
          description: '所有处理都在您的浏览器本地进行。图片永远不会上传到任何服务器。',
        },
        batch: {
          title: '批量处理',
          description: '同时处理多张图片，具有进度跟踪和批量下载功能。',
        },
      },
      errors: {
        noValidImages: '未找到有效的图片文件',
        compressionFailed: '压缩 {filename} 失败',
      },
      success: {
        compressionComplete: '所有图片压缩完成！',
        downloadComplete: '批量下载完成！',
        pasteSuccess: '图片粘贴成功！',
      },
    },
    imageWatermark: {
      title: '图片水印大师',
      description: '为您的照片添加文字或图片水印，支持自定义样式和位置',
      settings: '水印设置',
      watermarkType: '水印类型',
      textWatermark: '文字水印',
      textWatermarkDesc: '为图片添加文字水印',
      imageWatermark: '图片水印',
      imageWatermarkDesc: '为图片添加图片水印',
      combinedWatermark: '组合水印',
      combinedWatermarkDesc: '同时添加文字和图片水印',
      textSettings: '文字设置',
      watermarkText: '水印文字',
      textPlaceholder: '输入水印文字',
      fontSize: '字体大小',
      fontColor: '字体颜色',
      fontFamily: '字体',
      imageSettings: '图片设置',
      watermarkImage: '水印图片',
      uploadWatermark: '上传水印图片',
      watermarkPreview: '水印预览',
      removeWatermark: '移除水印',
      imageWidth: '图片宽度',
      imageOpacity: '图片透明度',
      positionSettings: '位置设置',
      position: '位置',
      margin: '边距',
      advancedSettings: '高级设置',
      opacity: '透明度',
      rotation: '旋转',
      scale: '缩放',
      topLeft: '左上角',
      topCenter: '顶部居中',
      topRight: '右上角',
      centerLeft: '左侧居中',
      center: '居中',
      centerRight: '右侧居中',
      bottomLeft: '左下角',
      bottomCenter: '底部居中',
      bottomRight: '右下角',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '支持多张图片，本地处理，不上传到服务器',
      supportedFormats: '支持格式',
      selectFiles: '选择文件',
      imageList: '图片列表',
      processing: '处理中...',
      processAll: '全部处理',
      downloadAll: '下载全部',
      process: '处理',
      remove: '移除',
      originalSize: '原始大小',
      processedSize: '处理后大小',
      processed: '已处理',
      original: '原始',
      status: {
        pending: '等待中',
        processing: '处理中',
        completed: '已完成',
        error: '失败',
      },
      features: {
        watermark: {
          title: '多种水印类型',
          description: '添加文字水印、图片水印或组合水印，支持完全自定义。',
        },
        batch: {
          title: '批量处理',
          description: '同时处理多张图片，具有进度跟踪和批量下载功能。',
        },
        customization: {
          title: '完全自定义',
          description: '调整水印位置、透明度、旋转、缩放、字体属性等。',
        },
      },
      errors: {
        noValidImages: '未找到有效的图片文件',
        invalidWatermark: '请选择有效的图片文件作为水印',
        noWatermarkImage: '请上传水印图片',
        noWatermarkText: '请输入水印文字',
        watermarkProcessingFailed: '处理水印图片失败',
        processingFailed: '处理 {filename} 失败',
      },
      success: {
        processingComplete: '所有图片处理完成！',
        downloadComplete: '批量下载完成！',
        pasteSuccess: '图片粘贴成功！',
      },
    },
    faviconGenerator: {
      title: '网站图标生成器',
      description: '从任意图片生成多种尺寸和格式的专业网站图标',
      uploadSection: '上传图片',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '上传任意图片来为您的网站创建图标',
      supportedFormats: '支持格式',
      selectImage: '选择图片',
      cropImage: '裁剪图片',
      originalImage: '原始图片',
      originalImageDescription: '完整分辨率图片预览 - 这是您的源图片',
      imageSize: '图片尺寸',
      cropPreview: '裁剪预览',
      selectAnother: '选择其他',
      cropInstruction:
        '拖动裁剪区域移动位置，或拖动角落手柄调整大小。选中的正方形区域将用于生成图标。',
      cropInstructionAdvanced:
        '拖动移动裁剪区域，拖动角落调整大小，或使用鼠标滚轮缩放。选中的正方形区域将用于生成网站图标。',
      outputFormat: '输出格式',
      sizes: '图标尺寸',
      generate: '生成图标',
      generating: '生成中...',
      generatedFavicons: '生成的图标',
      downloadAll: '下载全部为ZIP',
      usageInstructions: '如何使用图标',
      htmlUsage: 'HTML 实现',
      tips: '最佳实践',
      tip1: '使用简单、易识别的设计，确保在小尺寸下清晰可见',
      tip2: '确保良好的对比度，以便在不同背景下都能清晰显示',
      tip3: '在各种设备和浏览器上测试您的图标',
      tip4: '将 favicon.ico 放在网站根目录以便自动检测',
      features: {
        cropping: {
          title: '智能裁剪',
          description: '交互式裁剪工具，从您的图片中选择完美的正方形区域，实时预览效果。',
        },
        multiSize: {
          title: '多种尺寸',
          description: '生成所有标准尺寸（16px到128px）的图标，确保在各种设备上的最佳兼容性。',
        },
        formats: {
          title: '多种格式',
          description: '支持导出ICO、PNG或JPG格式，满足不同浏览器和平台的要求。',
        },
      },
      errors: {
        invalidFile: '请选择有效的图片文件',
        generationFailed: '生成图标失败',
        downloadFailed: '下载文件失败',
        imageLoadFailed: '图片加载失败',
        fileReadFailed: '文件读取失败',
      },
      success: {
        imageLoaded: '图片加载成功！',
        generationComplete: '图标生成成功！',
        downloadComplete: '下载完成！',
      },
    },
    jsonToSql: {
      title: 'JSON 转 SQL 转换器',
      description: '从 JSON 数据生成 SQL INSERT、UPDATE 或 CREATE TABLE 语句',
      inputTitle: '输入 JSON 数据',
      outputTitle: 'SQL 输出',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      noResults: '暂无生成的 SQL 语句。请输入 JSON 数据并配置选项。',
      conversionComplete: 'SQL 生成完成！',
      statementsGenerated: '已生成 {count} 条 SQL 语句',
      convert: '生成 SQL',
      options: {
        title: 'SQL 选项',
        tableName: '表名',
        tableNamePlaceholder: '输入表名',
        sqlType: 'SQL 类型',
        whereField: 'WHERE 字段',
        whereFieldPlaceholder: 'WHERE 子句的字段',
        escapeValues: '转义值',
        batchInsert: '批量插入',
      },
      features: {
        insertion: {
          title: '多种 SQL 类型',
          description: '从 JSON 数据生成 INSERT、UPDATE 或 CREATE TABLE 语句。',
        },
        customization: {
          title: '自定义选项',
          description: '配置表名、SQL 类型和字段映射以适应您的数据库。',
        },
        security: {
          title: 'SQL 安全',
          description: '自动值转义以防止 SQL 注入漏洞。',
        },
      },
      errors: {
        emptyInput: '请提供要转换的 JSON 数据',
        emptyTableName: '请提供表名',
        emptyWhereField: '请为 UPDATE 语句提供 WHERE 字段',
        invalidJson: 'JSON 格式无效。请检查您的输入。',
        notArray: '输入必须是 JSON 数组',
        emptyArray: 'JSON 数组不能为空',
        conversionFailed: '生成 SQL 语句失败',
      },
      success: {
        conversionComplete: 'SQL 语句生成成功！',
      },
    },
    universalConverter: {
      title: '通用格式转换器',
      description: '在 JSON、XML 和 HTTP 查询参数之间实时转换',
      inputTitle: '输入',
      outputTitle: '输出',
      format: '格式',
      formatButton: '格式化',
      conversionDirection: '转换方向',
      conversionDirectionDescription: '选择转换方向或交换面板',
      swap: '交换面板',
      convertLeft: '转换到左侧',
      convertRight: '转换到右侧',
      features: {
        bidirectional: {
          title: '双向转换',
          description: '支持任意格式之间的双向转换',
        },
        realtime: {
          title: '实时转换',
          description: '输入时即时转换，自动检测格式',
        },
        validation: {
          title: '格式验证',
          description: '内置所有支持格式的验证功能，提供详细的错误信息',
        },
      },
      errors: {
        conversionFailed: '转换失败。请检查您的输入格式。',
        unsupportedFormat: '选择了不支持的格式',
        invalidJson: 'JSON 格式无效。请检查您的输入。',
        invalidXml: 'XML 格式无效。请检查您的输入。',
        invalidQuery: '查询参数格式无效。请检查您的输入。',
        xmlGenerationFailed: '生成 XML 输出失败',
        queryGenerationFailed: '生成查询参数输出失败',
        formatFailed: '格式化失败。请检查您的输入格式。',
      },
    },
  },
}
