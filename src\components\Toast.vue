<template>
  <Teleport to="body">
    <Transition
      enter-active-class="duration-300 ease-out"
      enter-from-class="transform translate-x-full opacity-0"
      enter-to-class="translate-x-0 opacity-100"
      leave-active-class="duration-200 ease-in"
      leave-from-class="translate-x-0 opacity-100"
      leave-to-class="transform translate-x-full opacity-0"
    >
      <div
        v-if="visible"
        :class="[
          'fixed top-4 right-4 z-50 max-w-sm w-full shadow-lg rounded-lg border',
          typeClasses,
          'flex items-start p-4 space-x-3',
        ]"
      >
        <!-- Icon -->
        <div class="flex-shrink-0">
          <component :is="iconComponent" class="w-6 h-6" />
        </div>

        <!-- Content -->
        <div class="flex-1 min-w-0">
          <div v-if="title" class="text-sm font-semibold mb-1">
            {{ title }}
          </div>
          <div class="text-sm">
            {{ message }}
          </div>
        </div>

        <!-- Close Button -->
        <button
          @click="close"
          class="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

export interface ToastProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  onClose?: () => void
}

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'info',
  duration: 5000,
})

const visible = ref(false)
let timeoutId: number | null = null

const typeClasses = computed(() => {
  const classes = {
    success: 'bg-green-50 border-green-200 text-green-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
  }
  return classes[props.type]
})

const iconComponent = computed(() => {
  const icons = {
    success: 'IconSuccess',
    error: 'IconError',
    warning: 'IconWarning',
    info: 'IconInfo',
  }
  return icons[props.type]
})

function close() {
  visible.value = false
  if (timeoutId) {
    clearTimeout(timeoutId)
  }
  setTimeout(() => {
    props.onClose?.()
  }, 200)
}

onMounted(() => {
  visible.value = true
  if (props.duration > 0) {
    timeoutId = setTimeout(close, props.duration) as unknown as number
  }
})
</script>

<!-- Icon Components -->
<script lang="ts">
import { defineComponent, h } from 'vue'

const IconSuccess = defineComponent({
  render: () =>
    h(
      'svg',
      {
        class: 'w-6 h-6 text-green-500',
        fill: 'currentColor',
        viewBox: '0 0 20 20',
      },
      [
        h('path', {
          'fill-rule': 'evenodd',
          d: 'M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z',
          'clip-rule': 'evenodd',
        }),
      ],
    ),
})

const IconError = defineComponent({
  render: () =>
    h(
      'svg',
      {
        class: 'w-6 h-6 text-red-500',
        fill: 'currentColor',
        viewBox: '0 0 20 20',
      },
      [
        h('path', {
          'fill-rule': 'evenodd',
          d: 'M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z',
          'clip-rule': 'evenodd',
        }),
      ],
    ),
})

const IconWarning = defineComponent({
  render: () =>
    h(
      'svg',
      {
        class: 'w-6 h-6 text-yellow-500',
        fill: 'currentColor',
        viewBox: '0 0 20 20',
      },
      [
        h('path', {
          'fill-rule': 'evenodd',
          d: 'M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z',
          'clip-rule': 'evenodd',
        }),
      ],
    ),
})

const IconInfo = defineComponent({
  render: () =>
    h(
      'svg',
      {
        class: 'w-6 h-6 text-blue-500',
        fill: 'currentColor',
        viewBox: '0 0 20 20',
      },
      [
        h('path', {
          'fill-rule': 'evenodd',
          d: 'M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z',
          'clip-rule': 'evenodd',
        }),
      ],
    ),
})

export { IconSuccess, IconError, IconWarning, IconInfo }
</script>
